import { useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { getTranslation, SUPPORTED_LANGUAGES, type SupportedLanguage } from '@/lib/i18n';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  path?: string;
  noIndex?: boolean;
}

const SEO_TRANSLATIONS = {
  en: {
    defaultTitle: "Free Typing Test - Improve Your WPM Speed | TypingTest",
    defaultDescription: "Take a free typing test to measure your WPM speed and accuracy. Practice typing in multiple languages with real-time feedback and detailed statistics.",
    defaultKeywords: "typing test, WPM test, typing speed, keyboard skills, typing practice, multi-language typing, words per minute, accuracy test, online typing, typing tutor",
    privacyTitle: "Privacy Policy | TypingTest",
    privacyDescription: "Learn about how TypingTest protects your privacy and handles your data during typing tests.",
    termsTitle: "Terms of Service | TypingTest", 
    termsDescription: "Read the terms and conditions for using TypingTest's free online typing speed test."
  },
  zh: {
    defaultTitle: "免费打字测试 - 提升您的打字速度 | 打字测试",
    defaultDescription: "免费在线打字测试，测量您的打字速度和准确率。支持多语言练习，实时反馈和详细统计。",
    defaultKeywords: "打字测试, 打字速度, 键盘技能, 打字练习, 多语言打字, 每分钟字数, 准确率测试, 在线打字, 打字教程",
    privacyTitle: "隐私政策 | 打字测试",
    privacyDescription: "了解打字测试如何保护您的隐私并处理您在打字测试期间的数据。",
    termsTitle: "服务条款 | 打字测试",
    termsDescription: "阅读使用打字测试免费在线打字速度测试的条款和条件。"
  },
  es: {
    defaultTitle: "Prueba de Mecanografía Gratis - Mejora tu Velocidad PPM | TypingTest",
    defaultDescription: "Realiza una prueba de mecanografía gratuita para medir tu velocidad PPM y precisión. Practica mecanografía en múltiples idiomas con retroalimentación en tiempo real.",
    defaultKeywords: "prueba de mecanografía, prueba PPM, velocidad de escritura, habilidades de teclado, práctica de mecanografía, mecanografía multiidioma",
    privacyTitle: "Política de Privacidad | TypingTest",
    privacyDescription: "Aprende cómo TypingTest protege tu privacidad y maneja tus datos durante las pruebas de mecanografía.",
    termsTitle: "Términos de Servicio | TypingTest",
    termsDescription: "Lee los términos y condiciones para usar la prueba gratuita de velocidad de mecanografía de TypingTest."
  },
  fr: {
    defaultTitle: "Test de Frappe Gratuit - Améliorez votre Vitesse MPM | TypingTest",
    defaultDescription: "Passez un test de frappe gratuit pour mesurer votre vitesse MPM et votre précision. Pratiquez la frappe en plusieurs langues avec des commentaires en temps réel.",
    defaultKeywords: "test de frappe, test MPM, vitesse de frappe, compétences clavier, pratique de frappe, frappe multilingue",
    privacyTitle: "Politique de Confidentialité | TypingTest",
    privacyDescription: "Découvrez comment TypingTest protège votre vie privée et gère vos données pendant les tests de frappe.",
    termsTitle: "Conditions d'Utilisation | TypingTest",
    termsDescription: "Lisez les termes et conditions d'utilisation du test gratuit de vitesse de frappe de TypingTest."
  }
};

export function SEOHead({ title, description, keywords, path = '', noIndex = false }: SEOHeadProps) {
  const { language, pathWithoutLang } = useLanguage();
  
  useEffect(() => {
    const currentPath = path || pathWithoutLang;
    const seoData = getSEOData(language, currentPath, title, description, keywords);
    
    // Update document title
    document.title = seoData.title;
    
    // Update meta tags
    updateMetaTag('description', seoData.description);
    updateMetaTag('keywords', seoData.keywords);
    updateMetaTag('robots', noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1');
    updateMetaTag('language', language);
    
    // Update Open Graph tags
    updateMetaProperty('og:title', seoData.title);
    updateMetaProperty('og:description', seoData.description);
    updateMetaProperty('og:url', `https://typingtest.vercel.app/${language}${currentPath}`);
    updateMetaProperty('og:locale', getOGLocale(language));
    
    // Update Twitter Card tags
    updateMetaTag('twitter:title', seoData.title);
    updateMetaTag('twitter:description', seoData.description);
    
    // Update canonical URL
    updateCanonicalUrl(`https://typingtest.vercel.app/${language}${currentPath}`);
    
    // Update hreflang tags
    updateHreflangTags(currentPath);
    
    // Update HTML lang attribute
    document.documentElement.lang = language;
    
  }, [language, pathWithoutLang, title, description, keywords, path, noIndex]);

  return null; // This component doesn't render anything
}

function getSEOData(language: SupportedLanguage, path: string, customTitle?: string, customDescription?: string, customKeywords?: string) {
  const translations = SEO_TRANSLATIONS[language];
  
  let title = customTitle;
  let description = customDescription;
  let keywords = customKeywords || translations.defaultKeywords;
  
  if (!title || !description) {
    if (path.includes('privacy-policy')) {
      title = title || translations.privacyTitle;
      description = description || translations.privacyDescription;
    } else if (path.includes('terms-of-service')) {
      title = title || translations.termsTitle;
      description = description || translations.termsDescription;
    } else {
      title = title || translations.defaultTitle;
      description = description || translations.defaultDescription;
    }
  }
  
  return { title, description, keywords };
}

function updateMetaTag(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateMetaProperty(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateCanonicalUrl(url: string) {
  let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
  if (!canonical) {
    canonical = document.createElement('link');
    canonical.rel = 'canonical';
    document.head.appendChild(canonical);
  }
  canonical.href = url;
}

function updateHreflangTags(currentPath: string) {
  // Remove existing hreflang tags
  const existingHreflang = document.querySelectorAll('link[hreflang]');
  existingHreflang.forEach(link => link.remove());
  
  // Add new hreflang tags
  SUPPORTED_LANGUAGES.forEach(lang => {
    const link = document.createElement('link');
    link.rel = 'alternate';
    link.hreflang = lang;
    link.href = `https://typingtest.vercel.app/${lang}${currentPath}`;
    document.head.appendChild(link);
  });
  
  // Add x-default hreflang
  const defaultLink = document.createElement('link');
  defaultLink.rel = 'alternate';
  defaultLink.hreflang = 'x-default';
  defaultLink.href = `https://typingtest.vercel.app/en${currentPath}`;
  document.head.appendChild(defaultLink);
}

function getOGLocale(language: SupportedLanguage): string {
  const localeMap = {
    en: 'en_US',
    zh: 'zh_CN',
    es: 'es_ES',
    fr: 'fr_FR'
  };
  return localeMap[language];
}
