const CACHE_NAME = 'typingtest-v2';
const STATIC_CACHE = 'typingtest-static-v2';
const DYNAMIC_CACHE = 'typingtest-dynamic-v2';
const SEO_CACHE = 'typingtest-seo-v2';

// Assets to cache immediately for better SEO performance
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/favicon.svg',
  '/favicon-16x16.svg',
  '/favicon-32x32.svg',
  '/apple-touch-icon.svg',
  '/manifest.json',
  '/robots.txt',
  '/sitemap.xml',
  // Multi-language pages for SEO
  '/en/',
  '/zh/',
  '/es/',
  '/fr/',
  '/en/privacy-policy',
  '/zh/privacy-policy',
  '/es/privacy-policy',
  '/fr/privacy-policy',
  '/en/terms-of-service',
  '/zh/terms-of-service',
  '/es/terms-of-service',
  '/fr/terms-of-service'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/text-samples\/.*/,
  /\/api\/test-results/
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE &&
                cacheName !== DYNAMIC_CACHE &&
                cacheName !== SEO_CACHE) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static assets
  if (request.destination === 'document' || 
      request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }

  // Default: network first, cache fallback
  event.respondWith(
    fetch(request)
      .catch(() => caches.match(request))
  );
});

// Handle API requests with cache-first strategy for text samples
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  // Cache text samples for offline use
  if (API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    try {
      const cache = await caches.open(DYNAMIC_CACHE);
      const cachedResponse = await cache.match(request);
      
      if (cachedResponse) {
        // Return cached version and update in background
        fetch(request)
          .then(response => {
            if (response.ok) {
              cache.put(request, response.clone());
            }
          })
          .catch(() => {}); // Ignore network errors
        
        return cachedResponse;
      }
      
      // No cache, fetch from network
      const response = await fetch(request);
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
      
    } catch (error) {
      // Network error, try cache
      const cache = await caches.open(DYNAMIC_CACHE);
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  }
  
  // For other API requests, use network-first
  try {
    return await fetch(request);
  } catch (error) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Handle static requests with cache-first strategy
async function handleStaticRequest(request) {
  try {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const response = await fetch(request);
    if (response.ok) {
      cache.put(request, response.clone());
    }
    return response;
    
  } catch (error) {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback for navigation requests
    if (request.destination === 'document') {
      return cache.match('/index.html');
    }
    
    throw error;
  }
}

// Background sync for test results
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-test-results') {
    event.waitUntil(syncTestResults());
  }
});

async function syncTestResults() {
  try {
    // Get pending test results from IndexedDB or localStorage
    // This would need to be implemented based on your data storage strategy
    console.log('Syncing test results in background...');
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// Push notifications (if needed in the future)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/favicon-32x32.svg',
      badge: '/favicon-16x16.svg',
      vibrate: [100, 50, 100],
      data: data.data || {}
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow('/')
  );
});
