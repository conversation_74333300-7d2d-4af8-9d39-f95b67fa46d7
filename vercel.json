{"buildCommand": "npm run vercel-build", "outputDirectory": "dist/public", "headers": [{"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/index.ts"}, {"source": "/(.*)", "destination": "/index.html"}]}