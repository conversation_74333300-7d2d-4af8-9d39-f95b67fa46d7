<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Validation Test - TypingTest</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 10px;
        }
        h2 {
            color: #334155;
            margin-top: 30px;
        }
        .test-section {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .checklist li:before {
            content: "☐ ";
            color: #3b82f6;
            font-weight: bold;
            margin-right: 8px;
        }
        .url-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            margin: 10px 0;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .result {
            background: #ecfdf5;
            border: 1px solid #10b981;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        .tool-link {
            display: inline-block;
            background: #6366f1;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .tool-link:hover {
            background: #4f46e5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SEO Validation Test for TypingTest</h1>
        
        <div class="test-section">
            <h2>🌐 Base URL Configuration</h2>
            <input type="text" id="baseUrl" class="url-input" 
                   value="https://typingtest.vercel.app" 
                   placeholder="Enter your website URL">
            <button class="btn" onclick="updateBaseUrl()">Update Base URL</button>
        </div>

        <div class="test-section">
            <h2>📋 Technical SEO Checklist</h2>
            <ul class="checklist">
                <li>Meta title tags are present and optimized (50-60 characters)</li>
                <li>Meta descriptions are present and compelling (150-160 characters)</li>
                <li>Meta keywords are relevant and not over-stuffed</li>
                <li>Canonical URLs are properly set</li>
                <li>Robots meta tags are configured correctly</li>
                <li>Viewport meta tag is present for mobile optimization</li>
                <li>Open Graph tags are implemented</li>
                <li>Twitter Card tags are implemented</li>
                <li>Structured data (JSON-LD) is present and valid</li>
                <li>Favicon and app icons are properly configured</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🌍 Multi-language SEO Checklist</h2>
            <ul class="checklist">
                <li>Hreflang tags are implemented for all language versions</li>
                <li>Language-specific URLs follow /lang/ pattern</li>
                <li>Each language has localized meta titles and descriptions</li>
                <li>Content is properly translated, not machine-translated</li>
                <li>Language selector is accessible and functional</li>
                <li>Default language (x-default) hreflang is set</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📄 Essential Files Checklist</h2>
            <ul class="checklist">
                <li>robots.txt is accessible and properly configured</li>
                <li>sitemap.xml is accessible and includes all pages</li>
                <li>sitemap.xml includes hreflang annotations</li>
                <li>All language versions are included in sitemap</li>
                <li>404 page is properly configured</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>⚡ Performance & Core Web Vitals</h2>
            <ul class="checklist">
                <li>Largest Contentful Paint (LCP) < 2.5s</li>
                <li>First Input Delay (FID) < 100ms</li>
                <li>Cumulative Layout Shift (CLS) < 0.1</li>
                <li>Images are optimized and use lazy loading</li>
                <li>Critical CSS is inlined</li>
                <li>Service Worker is registered for caching</li>
                <li>HTTPS is enforced</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📱 Mobile SEO Checklist</h2>
            <ul class="checklist">
                <li>Responsive design works on all screen sizes</li>
                <li>Touch targets are appropriately sized (44px minimum)</li>
                <li>Text is readable without zooming</li>
                <li>Mobile page speed is optimized</li>
                <li>No intrusive interstitials</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔧 Quick Test Tools</h2>
            <button class="btn" onclick="testPage('/')">Test Homepage</button>
            <button class="btn" onclick="testPage('/en/privacy-policy')">Test Privacy Policy</button>
            <button class="btn" onclick="testPage('/zh/')">Test Chinese Version</button>
            <button class="btn" onclick="testRobots()">Test robots.txt</button>
            <button class="btn" onclick="testSitemap()">Test sitemap.xml</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🛠️ External SEO Tools</h2>
            <p>Use these tools to validate your SEO implementation:</p>
            
            <a href="#" class="tool-link" onclick="openTool('pagespeed')">Google PageSpeed Insights</a>
            <a href="#" class="tool-link" onclick="openTool('lighthouse')">Lighthouse</a>
            <a href="#" class="tool-link" onclick="openTool('search-console')">Google Search Console</a>
            <a href="#" class="tool-link" onclick="openTool('structured-data')">Structured Data Testing</a>
            <a href="#" class="tool-link" onclick="openTool('mobile-friendly')">Mobile-Friendly Test</a>
            <a href="#" class="tool-link" onclick="openTool('rich-results')">Rich Results Test</a>
        </div>

        <div class="test-section">
            <h2>📊 Analytics & Monitoring</h2>
            <ul class="checklist">
                <li>Google Analytics is properly configured</li>
                <li>Google Search Console is set up</li>
                <li>Core Web Vitals are being monitored</li>
                <li>SEO events are being tracked</li>
                <li>Language switching is being tracked</li>
                <li>Typing test completions are being tracked</li>
            </ul>
        </div>
    </div>

    <script>
        let baseUrl = 'https://typingtest.vercel.app';

        function updateBaseUrl() {
            baseUrl = document.getElementById('baseUrl').value.replace(/\/$/, '');
            showResult('Base URL updated to: ' + baseUrl, 'result');
        }

        function testPage(path) {
            const fullUrl = baseUrl + path;
            showResult(`Testing: ${fullUrl}`, 'result');
            
            // Open the page in a new tab for manual inspection
            window.open(fullUrl, '_blank');
            
            // You could add automated tests here using fetch() to check:
            // - Response status
            // - Meta tags presence
            // - Page load time
            // etc.
        }

        function testRobots() {
            const robotsUrl = baseUrl + '/robots.txt';
            showResult(`Testing robots.txt: ${robotsUrl}`, 'result');
            window.open(robotsUrl, '_blank');
        }

        function testSitemap() {
            const sitemapUrl = baseUrl + '/sitemap.xml';
            showResult(`Testing sitemap.xml: ${sitemapUrl}`, 'result');
            window.open(sitemapUrl, '_blank');
        }

        function openTool(tool) {
            const encodedUrl = encodeURIComponent(baseUrl);
            const tools = {
                'pagespeed': `https://pagespeed.web.dev/report?url=${encodedUrl}`,
                'lighthouse': `https://web.dev/measure/?url=${encodedUrl}`,
                'search-console': 'https://search.google.com/search-console',
                'structured-data': `https://search.google.com/test/rich-results?url=${encodedUrl}`,
                'mobile-friendly': `https://search.google.com/test/mobile-friendly?url=${encodedUrl}`,
                'rich-results': `https://search.google.com/test/rich-results?url=${encodedUrl}`
            };
            
            if (tools[tool]) {
                window.open(tools[tool], '_blank');
            }
        }

        function showResult(message, type = 'result') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `result ${type}`;
            resultElement.textContent = message;
            resultsDiv.appendChild(resultElement);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (resultElement.parentNode) {
                    resultElement.parentNode.removeChild(resultElement);
                }
            }, 5000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showResult('SEO Validation Test loaded. Update the base URL and start testing!', 'result');
        });
    </script>
</body>
</html>
