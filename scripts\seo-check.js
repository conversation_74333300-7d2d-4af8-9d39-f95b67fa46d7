#!/usr/bin/env node

/**
 * SEO检查脚本
 * 用于验证网站的SEO优化实施情况
 */

const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.VERCEL_URL || 'https://typingtest.vercel.app';
const LANGUAGES = ['en', 'zh', 'es', 'fr'];
const PAGES = ['', 'privacy-policy', 'terms-of-service'];

console.log('🔍 SEO检查开始...\n');

// 检查必要文件是否存在
function checkRequiredFiles() {
  console.log('📁 检查必要的SEO文件...');
  
  const requiredFiles = [
    'client/public/robots.txt',
    'client/public/sitemap.xml',
    'client/public/manifest.json',
    'client/public/favicon.svg',
    'client/public/apple-touch-icon.svg'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - 存在`);
    } else {
      console.log(`❌ ${file} - 缺失`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// 检查robots.txt内容
function checkRobotsTxt() {
  console.log('\n🤖 检查robots.txt内容...');
  
  const robotsPath = 'client/public/robots.txt';
  if (!fs.existsSync(robotsPath)) {
    console.log('❌ robots.txt文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(robotsPath, 'utf8');
  const checks = [
    { pattern: /User-agent: \*/, name: '通用用户代理' },
    { pattern: /Allow: \//, name: '允许根目录' },
    { pattern: /Sitemap:/, name: 'Sitemap声明' },
    { pattern: /Disallow: \/api\//, name: '禁止API目录' }
  ];
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} - 配置正确`);
    } else {
      console.log(`❌ ${check.name} - 配置缺失`);
    }
  });
  
  return true;
}

// 检查sitemap.xml内容
function checkSitemapXml() {
  console.log('\n🗺️ 检查sitemap.xml内容...');
  
  const sitemapPath = 'client/public/sitemap.xml';
  if (!fs.existsSync(sitemapPath)) {
    console.log('❌ sitemap.xml文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(sitemapPath, 'utf8');
  const checks = [
    { pattern: /<urlset/, name: 'XML结构' },
    { pattern: /hreflang="en"/, name: '英语hreflang' },
    { pattern: /hreflang="zh"/, name: '中文hreflang' },
    { pattern: /hreflang="x-default"/, name: '默认hreflang' },
    { pattern: /<lastmod>/, name: '最后修改时间' },
    { pattern: /<changefreq>/, name: '更新频率' },
    { pattern: /<priority>/, name: '优先级' }
  ];
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} - 配置正确`);
    } else {
      console.log(`❌ ${check.name} - 配置缺失`);
    }
  });
  
  return true;
}

// 检查HTML文件中的SEO标签
function checkHtmlSeoTags() {
  console.log('\n📄 检查HTML SEO标签...');
  
  const htmlPath = 'client/index.html';
  if (!fs.existsSync(htmlPath)) {
    console.log('❌ index.html文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(htmlPath, 'utf8');
  const checks = [
    { pattern: /<title>/, name: 'Title标签' },
    { pattern: /<meta name="description"/, name: 'Description meta标签' },
    { pattern: /<meta name="keywords"/, name: 'Keywords meta标签' },
    { pattern: /<meta name="viewport"/, name: 'Viewport meta标签' },
    { pattern: /<meta name="robots"/, name: 'Robots meta标签' },
    { pattern: /<link rel="canonical"/, name: 'Canonical链接' },
    { pattern: /<link rel="alternate" hreflang=/, name: 'Hreflang链接' },
    { pattern: /<meta property="og:title"/, name: 'Open Graph标题' },
    { pattern: /<meta property="og:description"/, name: 'Open Graph描述' },
    { pattern: /<meta name="twitter:card"/, name: 'Twitter Card' },
    { pattern: /<script type="application\/ld\+json">/, name: '结构化数据' },
    { pattern: /gtag/, name: 'Google Analytics' }
  ];
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} - 存在`);
    } else {
      console.log(`❌ ${check.name} - 缺失`);
    }
  });
  
  return true;
}

// 检查SEO组件文件
function checkSeoComponents() {
  console.log('\n🧩 检查SEO组件文件...');
  
  const components = [
    'client/src/components/SEOHead.tsx',
    'client/src/components/GoogleSearchConsole.tsx',
    'client/src/components/PerformanceOptimizer.tsx'
  ];
  
  components.forEach(component => {
    if (fs.existsSync(component)) {
      console.log(`✅ ${path.basename(component)} - 存在`);
    } else {
      console.log(`❌ ${path.basename(component)} - 缺失`);
    }
  });
  
  return true;
}

// 生成SEO URL列表
function generateSeoUrls() {
  console.log('\n🔗 生成SEO URL列表...');
  
  const urls = [];
  
  // 添加主页和子页面的所有语言版本
  LANGUAGES.forEach(lang => {
    PAGES.forEach(page => {
      const url = page ? `${BASE_URL}/${lang}/${page}` : `${BASE_URL}/${lang}/`;
      urls.push(url);
    });
  });
  
  // 添加特殊文件
  urls.push(`${BASE_URL}/robots.txt`);
  urls.push(`${BASE_URL}/sitemap.xml`);
  urls.push(`${BASE_URL}/manifest.json`);
  
  console.log('📋 需要测试的URL列表:');
  urls.forEach(url => {
    console.log(`   ${url}`);
  });
  
  return urls;
}

// 生成SEO测试报告
function generateSeoReport() {
  console.log('\n📊 生成SEO测试报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    languages: LANGUAGES,
    pages: PAGES,
    checks: {
      requiredFiles: checkRequiredFiles(),
      robotsTxt: checkRobotsTxt(),
      sitemapXml: checkSitemapXml(),
      htmlSeoTags: checkHtmlSeoTags(),
      seoComponents: checkSeoComponents()
    },
    urls: generateSeoUrls()
  };
  
  // 保存报告到文件
  const reportPath = 'seo-check-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📄 SEO检查报告已保存到: ${reportPath}`);
  
  return report;
}

// 主函数
function main() {
  console.log(`🌐 检查网站: ${BASE_URL}`);
  console.log(`🌍 支持语言: ${LANGUAGES.join(', ')}`);
  console.log(`📄 检查页面: ${PAGES.join(', ')}\n`);
  
  const report = generateSeoReport();
  
  // 统计结果
  const totalChecks = Object.keys(report.checks).length;
  const passedChecks = Object.values(report.checks).filter(Boolean).length;
  
  console.log('\n📈 检查结果汇总:');
  console.log(`✅ 通过检查: ${passedChecks}/${totalChecks}`);
  console.log(`📊 成功率: ${Math.round((passedChecks / totalChecks) * 100)}%`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 所有SEO检查都通过了！');
  } else {
    console.log('\n⚠️ 部分SEO检查未通过，请查看上述详细信息。');
  }
  
  console.log('\n🔧 下一步建议:');
  console.log('1. 使用 /tests/seo-validation.html 进行手动验证');
  console.log('2. 在Google PageSpeed Insights中测试性能');
  console.log('3. 在Google Search Console中验证网站');
  console.log('4. 使用Lighthouse进行综合审计');
  
  console.log('\n✨ SEO检查完成！');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkRequiredFiles,
  checkRobotsTxt,
  checkSitemapXml,
  checkHtmlSeoTags,
  checkSeoComponents,
  generateSeoUrls,
  generateSeoReport
};
