# TypingTest Web

一个支持多语言的打字测试网站，具有响应式设计和分析功能。

## 🌟 特性

- **多语言支持**: 支持中文、英文、西班牙语等多种语言
- **响应式设计**: 完美适配桌面端和移动端
- **实时统计**: WPM（每分钟字数）、准确率、错误统计
- **Google Analytics**: 完整的用户行为分析
- **性能优化**: 懒加载、代码分割、PWA支持
- **无障碍访问**: 符合WCAG标准的无障碍设计

## 🛠️ 技术栈

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Radix UI** - 无障碍UI组件
- **Framer Motion** - 动画库
- **Wouter** - 轻量级路由
- **Vite** - 构建工具

### 后端
- **Express.js** - Web服务器
- **Drizzle ORM** - 数据库ORM
- **Neon Database** - PostgreSQL数据库
- **Zod** - 数据验证

### 部署
- **Vercel** - 部署平台
- **Vercel Functions** - 无服务器函数

## 📁 项目结构

```
typingtest-web/
├── client/                 # 前端代码
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── lib/            # 工具函数
│   │   ├── pages/          # 页面组件
│   │   └── contexts/       # React Context
│   └── public/             # 静态资源
├── server/                 # 后端代码
│   ├── index.ts           # 服务器入口
│   ├── routes.ts          # API路由
│   └── storage.ts         # 数据存储
├── api/                   # Vercel Functions
├── shared/                # 共享类型和模式
├── docs/                  # 项目文档
└── tests/                 # 测试文件
```

## 🚀 开发

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发服务器
```bash
npm run dev
```

### 构建
```bash
npm run build
```

### 部署到Vercel
```bash
npm run vercel-build
```

## 📊 测试

### 移动端兼容性测试
在浏览器控制台运行：
```javascript
testMobileCompatibility()
```

### Google Analytics测试
访问 `/tests/test-analytics.html` 进行GA集成测试

### API端点测试
```bash
node tests/test-api-endpoints.js
```

## 🌍 多语言支持

项目支持以下语言：
- 中文 (zh)
- 英文 (en) 
- 西班牙语 (es)

URL结构：`/{language}/{page}`

## 📈 分析

集成了Google Analytics (G-RCM4MX2590)，跟踪：
- 页面浏览量
- 测试完成事件
- 用户交互事件
- 性能指标

## 🔧 配置

主要配置文件：
- `vercel.json` - Vercel部署配置
- `tailwind.config.ts` - Tailwind CSS配置
- `vite.config.ts` - Vite构建配置
- `drizzle.config.ts` - 数据库配置

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
