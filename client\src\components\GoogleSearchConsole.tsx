import { useEffect } from 'react';

interface GoogleSearchConsoleProps {
  verificationCode?: string;
}

export function GoogleSearchConsole({ verificationCode }: GoogleSearchConsoleProps) {
  useEffect(() => {
    // Add Google Search Console verification meta tag
    if (verificationCode) {
      addVerificationMeta('google-site-verification', verificationCode);
    }
    
    // Add Bing Webmaster Tools verification (optional)
    // addVerificationMeta('msvalidate.01', 'YOUR_BING_VERIFICATION_CODE');
    
    // Add Yandex verification (optional)
    // addVerificationMeta('yandex-verification', 'YOUR_YANDEX_VERIFICATION_CODE');
    
    // Add Baidu verification (optional for Chinese market)
    // addVerificationMeta('baidu-site-verification', 'YOUR_BAIDU_VERIFICATION_CODE');
    
  }, [verificationCode]);

  return null; // This component doesn't render anything
}

function addVerificationMeta(name: string, content: string) {
  // Check if meta tag already exists
  let existingMeta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  
  if (!existingMeta) {
    const meta = document.createElement('meta');
    meta.name = name;
    meta.content = content;
    document.head.appendChild(meta);
  } else {
    existingMeta.content = content;
  }
}

// Enhanced Google Analytics configuration for SEO
export function enhanceGoogleAnalytics() {
  if (typeof window !== 'undefined' && window.gtag) {
    // Track page views for SEO
    window.gtag('config', 'G-RCM4MX2590', {
      // Enhanced ecommerce for better SEO insights
      enhanced_ecommerce: true,
      
      // Site search tracking
      site_search_query_parameter: 'q',
      
      // Custom dimensions for SEO tracking
      custom_map: {
        'custom_dimension_1': 'language',
        'custom_dimension_2': 'test_duration',
        'custom_dimension_3': 'wpm_score',
        'custom_dimension_4': 'accuracy_score'
      }
    });
    
    // Track typing test completions as conversions
    window.gtag('event', 'page_view', {
      page_title: document.title,
      page_location: window.location.href,
      language: document.documentElement.lang
    });
  }
}

// Track SEO-relevant events
export function trackSEOEvent(eventName: string, parameters: Record<string, any> = {}) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      ...parameters,
      language: document.documentElement.lang,
      page_location: window.location.href
    });
  }
}

// Track typing test completion for SEO insights
export function trackTypingTestCompletion(wpm: number, accuracy: number, language: string, duration: number) {
  trackSEOEvent('typing_test_completed', {
    wpm_score: wpm,
    accuracy_score: accuracy,
    test_language: language,
    test_duration: duration,
    event_category: 'engagement',
    event_label: `${language}_${duration}s`
  });
}

// Track language switching for multilingual SEO insights
export function trackLanguageSwitch(fromLanguage: string, toLanguage: string) {
  trackSEOEvent('language_switch', {
    from_language: fromLanguage,
    to_language: toLanguage,
    event_category: 'navigation',
    event_label: `${fromLanguage}_to_${toLanguage}`
  });
}
