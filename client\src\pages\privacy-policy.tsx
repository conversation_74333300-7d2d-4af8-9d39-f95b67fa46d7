import { <PERSON> } from "wouter";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getTranslation } from "@/lib/i18n";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAnalytics } from "@/hooks/use-analytics";
import { SEOHead } from "@/components/SEOHead";
import { PerformanceOptimizer } from "@/components/PerformanceOptimizer";

export default function PrivacyPolicy() {
  const { language, buildUrl } = useLanguage();

  // 跟踪页面访问
  useAnalytics('privacy-policy', language);

  return (
    <div className="bg-slate-50 font-sans min-h-screen">
      {/* SEO Components */}
      <SEOHead path="/privacy-policy" />
      <PerformanceOptimizer />

      <header className="bg-white border-b border-slate-200" role="banner">
        <nav className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8" role="navigation" aria-label="Breadcrumb navigation">
          <div className="flex items-center h-16">
            <Link href={buildUrl('/')}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" aria-hidden="true" />
                {getTranslation(language, 'backToApp')}
              </Button>
            </Link>
          </div>
        </nav>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8" role="main">
        <article className="bg-white rounded-xl shadow-sm border border-slate-200 p-8">
          <header>
            <h1 className="text-3xl font-bold text-slate-800 mb-8">{getTranslation(language, 'privacyPolicy')}</h1>
          </header>
          
          <div className="prose prose-slate max-w-none">
            <p className="text-slate-600 mb-6">
              {getTranslation(language, 'lastUpdated')} {new Date().toLocaleDateString()}
            </p>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'informationWeCollect')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'privacyContent1')}
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>{getTranslation(language, 'privacyList1')}</li>
                <li>{getTranslation(language, 'privacyList2')}</li>
                <li>{getTranslation(language, 'privacyList3')}</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'howWeUseInfo')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'howWeUseContent')}
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>{getTranslation(language, 'useList1')}</li>
                <li>{getTranslation(language, 'useList2')}</li>
                <li>{getTranslation(language, 'useList3')}</li>
                <li>{getTranslation(language, 'useList4')}</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'dataStorage')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'dataStorageContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'thirdPartyServices')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'thirdPartyContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'dataSecurity')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'dataSecurityContent')}
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">{getTranslation(language, 'contactUs')}</h2>
              <p className="text-slate-600 mb-4">
                {getTranslation(language, 'contactUsContent')}
              </p>
            </section>
          </div>
        </article>
      </main>
    </div>
  );
}